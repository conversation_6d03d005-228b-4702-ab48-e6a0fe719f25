import React, { useState, useEffect, useRef } from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MessageCircle, Users, Shield, Zap, Send, Clock } from 'lucide-react';
import { ref, push, onValue, off } from 'firebase/database';
import { database } from '@/config/firebaseConfig';

// تعريف نوع الرسالة
interface Message {
  id: string;
  username: string;
  message: string;
  timestamp: number;
  userId: string;
}

// تعريف نوع المستخدم
interface User {
  username: string;
  userId: string;
}

const GroupChat: React.FC = () => {
  // حالات المكون
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [user, setUser] = useState<User | null>(null);
  const [tempUsername, setTempUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // مراجع للعناصر
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // دالة للتمرير إلى أسفل الدردشة
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // تأثير للتمرير عند إضافة رسائل جديدة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تأثير لتحميل المستخدم من localStorage
  useEffect(() => {
    const savedUser = localStorage.getItem('chatUser');
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('chatUser');
      }
    }
  }, []);

  // تأثير لتحميل الرسائل من Firebase
  useEffect(() => {
    if (!user) return;

    const messagesRef = ref(database, 'messages');

    // الاستماع للرسائل الجديدة
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messagesList: Message[] = Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        }));

        // ترتيب الرسائل حسب التوقيت
        messagesList.sort((a, b) => a.timestamp - b.timestamp);
        setMessages(messagesList);
      } else {
        setMessages([]);
      }
    });

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      off(messagesRef, 'value', unsubscribe);
    };
  }, [user]);

  // دالة لحفظ اسم المستخدم
  const handleSaveUsername = () => {
    if (tempUsername.trim().length < 2) {
      alert('يجب أن يكون الاسم المستعار أكثر من حرفين');
      return;
    }

    const userId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newUser: User = {
      username: tempUsername.trim(),
      userId: userId
    };

    setUser(newUser);
    localStorage.setItem('chatUser', JSON.stringify(newUser));
    setTempUsername('');
  };

  // دالة لإرسال رسالة جديدة
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || isLoading) return;

    setIsLoading(true);

    try {
      const messagesRef = ref(database, 'messages');
      const messageData = {
        username: user.username,
        message: newMessage.trim(),
        timestamp: Date.now(),
        userId: user.userId
      };

      await push(messagesRef, messageData);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة للتعامل مع الضغط على Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!user) {
        handleSaveUsername();
      } else {
        handleSendMessage();
      }
    }
  };

  // دالة لتنسيق التوقيت
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />

      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">الدردشة</span> الجماعية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              تواصل مع زوار الموقع الآخرين في دردشة جماعية مباشرة وآمنة
            </p>

            {/* ميزات الدردشة */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
              <Card className="bg-gray-900/30 border-amber-500/20">
                <CardContent className="p-4 text-center">
                  <Zap className="w-8 h-8 text-amber-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-amber-400 mb-1">مباشرة</h3>
                  <p className="text-xs text-gray-400">رسائل فورية بدون تأخير</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-green-500/20">
                <CardContent className="p-4 text-center">
                  <Users className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-400 mb-1">جماعية</h3>
                  <p className="text-xs text-gray-400">تواصل مع عدة أشخاص</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-blue-500/20">
                <CardContent className="p-4 text-center">
                  <Shield className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-400 mb-1">آمنة</h3>
                  <p className="text-xs text-gray-400">بدون تسجيل أو بيانات شخصية</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-purple-500/20">
                <CardContent className="p-4 text-center">
                  <MessageCircle className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-400 mb-1">سهلة</h3>
                  <p className="text-xs text-gray-400">واجهة بسيطة وسهلة الاستخدام</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* مكون الدردشة */}
          <div className="mb-12">
            <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl text-amber-400 flex items-center gap-2">
                  <MessageCircle className="w-6 h-6" />
                  الدردشة المباشرة
                  <span className="text-sm text-gray-400 ml-auto">
                    {messages.length} رسالة
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!user ? (
                  // نموذج إدخال اسم المستخدم
                  <div className="text-center py-8">
                    <h3 className="text-xl mb-4">أدخل اسمك المستعار للانضمام للدردشة</h3>
                    <div className="max-w-md mx-auto flex gap-2">
                      <Input
                        type="text"
                        placeholder="اسمك المستعار..."
                        value={tempUsername}
                        onChange={(e) => setTempUsername(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="bg-gray-700 border-gray-600 text-white"
                        maxLength={20}
                      />
                      <Button
                        onClick={handleSaveUsername}
                        className="bg-amber-500 hover:bg-amber-600 text-black"
                        disabled={tempUsername.trim().length < 2}
                      >
                        انضمام
                      </Button>
                    </div>
                  </div>
                ) : (
                  // واجهة الدردشة
                  <div className="space-y-4">
                    {/* منطقة الرسائل */}
                    <div className="h-96 overflow-y-auto bg-gray-900/50 rounded-lg p-4 space-y-3">
                      {messages.length === 0 ? (
                        <div className="text-center text-gray-400 py-8">
                          <MessageCircle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                          <p>لا توجد رسائل بعد. كن أول من يبدأ المحادثة!</p>
                        </div>
                      ) : (
                        messages.map((msg) => (
                          <div
                            key={msg.id}
                            className={`flex ${msg.userId === user.userId ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                msg.userId === user.userId
                                  ? 'bg-amber-500 text-black'
                                  : 'bg-gray-700 text-white'
                              }`}
                            >
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-semibold text-sm">
                                  {msg.username}
                                </span>
                                <span className="text-xs opacity-70 flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {formatTime(msg.timestamp)}
                                </span>
                              </div>
                              <p className="text-sm break-words">{msg.message}</p>
                            </div>
                          </div>
                        ))
                      )}
                      <div ref={messagesEndRef} />
                    </div>

                    {/* نموذج إرسال الرسائل */}
                    <div className="flex gap-2">
                      <Input
                        type="text"
                        placeholder="اكتب رسالتك..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="bg-gray-700 border-gray-600 text-white flex-1"
                        maxLength={500}
                        disabled={isLoading}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isLoading}
                        className="bg-amber-500 hover:bg-amber-600 text-black"
                      >
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* معلومات المستخدم */}
                    <div className="text-center text-sm text-gray-400">
                      مرحباً <span className="text-amber-400">{user.username}</span>
                      <Button
                        variant="link"
                        size="sm"
                        onClick={() => {
                          setUser(null);
                          localStorage.removeItem('chatUser');
                        }}
                        className="text-gray-400 hover:text-white ml-2"
                      >
                        تغيير الاسم
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* معلومات إضافية */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="bg-gray-900/30 border-amber-500/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-amber-400 mb-4 flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  كيفية الاستخدام
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</span>
                    <p>أدخل اسمك المستعار (لا يتطلب تسجيل)</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</span>
                    <p>انضم إلى الدردشة وشاهد الرسائل السابقة</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</span>
                    <p>اكتب رسالتك واضغط Enter أو زر الإرسال</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</span>
                    <p>تفاعل مع الزوار الآخرين في الوقت الفعلي</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/30 border-green-500/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-green-400 mb-4 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  الخصوصية والأمان
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>لا نطلب أي معلومات شخصية</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>الاسم المستعار يُحفظ محلياً فقط</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>الرسائل مشفرة ومحمية</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>يمكنك تغيير اسمك في أي وقت</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>لا نتتبع أو نحفظ بياناتك الشخصية</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* ملاحظة تقنية */}
          <div className="mt-12">
            <Card className="bg-blue-900/20 border-blue-500/30">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-bold text-blue-400 mb-2">⚡ تقنية متقدمة</h3>
                <p className="text-gray-300 text-sm">
                  تم بناء هذه الدردشة باستخدام <span className="text-amber-400 font-semibold">React</span> و
                  <span className="text-amber-400 font-semibold"> TypeScript</span> و
                  <span className="text-amber-400 font-semibold"> Firebase Realtime Database</span>
                  لضمان أداء سريع وموثوق ومجاني 100%
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default GroupChat;
