import React from 'react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import LiveGroupChat from '@/components/LiveGroupChat';
import { Card, CardContent } from '@/components/ui/card';
import { MessageCircle, Users, Shield, Zap } from 'lucide-react';

const GroupChat: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">الدردشة</span> الجماعية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              تواصل مع زوار الموقع الآخرين في دردشة جماعية مباشرة وآمنة
            </p>

            {/* ميزات الدردشة */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
              <Card className="bg-gray-900/30 border-amber-500/20">
                <CardContent className="p-4 text-center">
                  <Zap className="w-8 h-8 text-amber-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-amber-400 mb-1">مباشرة</h3>
                  <p className="text-xs text-gray-400">رسائل فورية بدون تأخير</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-green-500/20">
                <CardContent className="p-4 text-center">
                  <Users className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-400 mb-1">جماعية</h3>
                  <p className="text-xs text-gray-400">تواصل مع عدة أشخاص</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-blue-500/20">
                <CardContent className="p-4 text-center">
                  <Shield className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-400 mb-1">آمنة</h3>
                  <p className="text-xs text-gray-400">بدون تسجيل أو بيانات شخصية</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-900/30 border-purple-500/20">
                <CardContent className="p-4 text-center">
                  <MessageCircle className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-400 mb-1">سهلة</h3>
                  <p className="text-xs text-gray-400">واجهة بسيطة وسهلة الاستخدام</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* مكون الدردشة */}
          <div className="mb-12">
            <LiveGroupChat />
          </div>

          {/* معلومات إضافية */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="bg-gray-900/30 border-amber-500/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-amber-400 mb-4 flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  كيفية الاستخدام
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</span>
                    <p>أدخل اسمك المستعار (لا يتطلب تسجيل)</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</span>
                    <p>انضم إلى الدردشة وشاهد الرسائل السابقة</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</span>
                    <p>اكتب رسالتك واضغط Enter أو زر الإرسال</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <span className="bg-amber-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</span>
                    <p>تفاعل مع الزوار الآخرين في الوقت الفعلي</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/30 border-green-500/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-green-400 mb-4 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  الخصوصية والأمان
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>لا نطلب أي معلومات شخصية</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>الاسم المستعار يُحفظ محلياً فقط</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>الرسائل مشفرة ومحمية</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>يمكنك تغيير اسمك في أي وقت</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <p>لا نتتبع أو نحفظ بياناتك الشخصية</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* ملاحظة تقنية */}
          <div className="mt-12">
            <Card className="bg-blue-900/20 border-blue-500/30">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-bold text-blue-400 mb-2">⚡ تقنية متقدمة</h3>
                <p className="text-gray-300 text-sm">
                  تم بناء هذه الدردشة باستخدام <span className="text-amber-400 font-semibold">React</span> و
                  <span className="text-amber-400 font-semibold"> TypeScript</span> و
                  <span className="text-amber-400 font-semibold"> Firebase Realtime Database</span>
                  لضمان أداء سريع وموثوق ومجاني 100%
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default GroupChat;
