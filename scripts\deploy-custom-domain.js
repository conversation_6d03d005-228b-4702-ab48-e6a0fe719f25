#!/usr/bin/env node

/**
 * سكريبت نشر شامل للنطاق المخصص
 * يتضمن جميع الخطوات المطلوبة لتشغيل الأخبار والدردشة
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية النشر للنطاق المخصص...\n');

// دالة لتنفيذ الأوامر
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} - تم بنجاح\n`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} - فشل`);
    console.error(error.message);
    return false;
  }
}

// دالة للتحقق من وجود الملفات
function checkFile(filePath, description) {
  console.log(`🔍 التحقق من ${description}...`);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description} موجود\n`);
    return true;
  } else {
    console.log(`❌ ${description} مفقود: ${filePath}\n`);
    return false;
  }
}

// الخطوة 1: التحقق من الملفات المطلوبة
console.log('1️⃣ التحقق من الملفات المطلوبة...');
const requiredFiles = [
  { path: 'netlify.toml', desc: 'ملف تكوين Netlify' },
  { path: 'public/_headers', desc: 'ملف CORS Headers' },
  { path: 'netlify/functions/news.js', desc: 'Netlify Function للأخبار' },
  { path: 'functions/src/index.ts', desc: 'Firebase Functions' },
  { path: 'src/config/firebaseConfig.ts', desc: 'تكوين Firebase' }
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (!checkFile(file.path, file.desc)) {
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('❌ بعض الملفات المطلوبة مفقودة. يرجى التأكد من وجودها قبل المتابعة.');
  process.exit(1);
}

// الخطوة 2: بناء المشروع
console.log('2️⃣ بناء المشروع...');
if (!runCommand('npm run build:production', 'بناء المشروع للإنتاج')) {
  console.log('❌ فشل في بناء المشروع');
  process.exit(1);
}

// الخطوة 3: بناء ونشر Firebase Functions
console.log('3️⃣ بناء ونشر Firebase Functions...');
if (!runCommand('cd functions && npm run build', 'بناء Firebase Functions')) {
  console.log('❌ فشل في بناء Firebase Functions');
  process.exit(1);
}

if (!runCommand('firebase deploy --only functions', 'نشر Firebase Functions')) {
  console.log('❌ فشل في نشر Firebase Functions');
  process.exit(1);
}

// الخطوة 4: نشر على Netlify (إذا كان متاحاً)
console.log('4️⃣ نشر على Netlify...');
try {
  execSync('which netlify', { stdio: 'ignore' });
  runCommand('netlify deploy --prod --dir=dist', 'نشر على Netlify');
} catch (error) {
  console.log('⚠️ Netlify CLI غير مثبت. سيتم النشر تلقائياً عبر Git push');
}

// الخطوة 5: اختبار النطاق المخصص
console.log('5️⃣ اختبار النطاق المخصص...');
if (fs.existsSync('scripts/test-custom-domain.js')) {
  runCommand('node scripts/test-custom-domain.js', 'اختبار النطاق المخصص');
} else {
  console.log('⚠️ سكريبت الاختبار غير موجود');
}

// الخطوة 6: عرض التعليمات النهائية
console.log('6️⃣ التعليمات النهائية...');
console.log('');
console.log('🎉 تم النشر بنجاح!');
console.log('');
console.log('📋 الخطوات التالية:');
console.log('');
console.log('1. 🔧 إعداد Firebase Console:');
console.log('   - انتقل إلى: https://console.firebase.google.com/project/myprofilewebsitechatproject');
console.log('   - Authentication > Settings > Authorized domains');
console.log('   - أضف: hodifatech.com');
console.log('');
console.log('2. 🌐 إعداد Netlify:');
console.log('   - انتقل إلى: https://app.netlify.com');
console.log('   - Site settings > Domain management');
console.log('   - أضف النطاق المخصص: hodifatech.com');
console.log('   - تفعيل Force HTTPS');
console.log('');
console.log('3. 🔑 Environment Variables في Netlify:');
console.log('   - Site settings > Environment variables');
console.log('   - أضف: NEWS_API_KEY=1660ff496c4247c3a7d49457501feb73');
console.log('');
console.log('4. 🌍 إعداد DNS:');
console.log('   - في مزود النطاق، أضف CNAME:');
console.log('   - hodifatech.com -> hodifa-tech-profile-main.netlify.app');
console.log('   - www.hodifatech.com -> hodifa-tech-profile-main.netlify.app');
console.log('');
console.log('5. ✅ اختبار النهائي:');
console.log('   - الأخبار: https://hodifatech.com/tech-news');
console.log('   - الدردشة: https://hodifatech.com/group-chat');
console.log('');
console.log('🔗 روابط مفيدة:');
console.log('   - Firebase Console: https://console.firebase.google.com');
console.log('   - Netlify Dashboard: https://app.netlify.com');
console.log('   - موقعك: https://hodifatech.com');
console.log('');
console.log('💡 في حالة وجود مشاكل:');
console.log('   - راجع ملف CUSTOM_DOMAIN_SOLUTION.md');
console.log('   - شغل: node scripts/test-custom-domain.js');
console.log('   - تحقق من Firebase Console logs');
console.log('');
