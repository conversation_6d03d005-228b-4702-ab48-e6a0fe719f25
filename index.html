
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>حذيفه الحذيفي - مهندس تقنية معلومات</title>
    <meta name="description" content="الموقع الشخصي لحذيفه عبدالمعز الحذيفي، مهندس تقنية معلومات متخصص في تطوير الويب وقواعد البيانات" />
    <meta name="keywords" content="مهندس تقنية معلومات, تطوير ويب, Laravel, قواعد البيانات, برمجة" />
    <meta name="author" content="حذيفه عبدالمعز الحذيفي" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/profile-photo.jpg" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="حذيفه الحذيفي - مهندس تقنية معلومات" />
    <meta property="og:description" content="الموقع الشخصي لحذيفه عبدالمعز الحذيفي، مهندس تقنية معلومات متخصص في تطوير الويب وقواعد البيانات" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.hodifatech.com/" />
    <meta property="og:image" content="https://www.hodifatech.com/profile-photo.jpg" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="حذيفه الحذيفي - مهندس تقنية معلومات" />
    <meta name="twitter:description" content="الموقع الشخصي لحذيفه عبدالمعز الحذيفي، مهندس تقنية معلومات متخصص في تطوير الويب وقواعد البيانات" />
    <meta name="twitter:image" content="https://www.hodifatech.com/profile-photo.jpg" />

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي",
      "jobTitle": "مهندس تقنية معلومات",
      "email": "<EMAIL>",
      "telephone": ["+967 777548421", "+967 718706242"],
      "url": "https://www.hodifatech.com/",
      "sameAs": [
        "https://www.facebook.com/share/1E3T83a8KD/",
        "https://www.linkedin.com/in/hodifa-al-hodify-30644b289",
        "https://x.com/moaz_abdh",
        "https://github.com/HA1234098765",
        "https://www.instagram.com/invites/contact/?utm_source=ig_contact_invite&utm_medium=copy_link&utm_content=mwfgwqx"
      ]
    }
    </script>

    <link rel="canonical" href="https://www.hodifatech.com/" />

    <!-- Firebase SDK for Chat -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script>
      // إعداد Firebase عالمي للدردشة
      if (typeof firebase !== 'undefined') {
        window.firebaseConfig = {
          apiKey: "AIzaSyC6NWVYptFUzjy8mNMzyuwC4PDTrRDVmU4",
          authDomain: "myprofilewebsitechatproject.firebaseapp.com",
          databaseURL: "https://myprofilewebsitechatproject-default-rtdb.europe-west1.firebasedatabase.app",
          projectId: "myprofilewebsitechatproject",
          storageBucket: "myprofilewebsitechatproject.firebasestorage.app",
          messagingSenderId: "868130500021",
          appId: "1:868130500021:web:f9e511213975f749793dfc"
        };

        try {
          // تهيئة Firebase
          firebase.initializeApp(window.firebaseConfig);
          window.firebaseDatabase = firebase.database();
          console.log('✅ Firebase initialized successfully for chat');
        } catch (error) {
          console.error('❌ Firebase initialization failed:', error);
        }
      } else {
        console.warn('⚠️ Firebase SDK not loaded');
      }
    </script>

  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
